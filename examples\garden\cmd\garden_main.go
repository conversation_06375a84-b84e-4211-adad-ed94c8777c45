package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	garden "github.com/ynl/greensoulai/examples/garden"
	"github.com/ynl/greensoulai/examples/garden/websocket"
	"github.com/ynl/greensoulai/pkg/events"
	"github.com/ynl/greensoulai/pkg/logger"
)

func main() {
	// 创建上下文和取消函数
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建事件总线
	var baseLogger logger.Logger
	if os.Getenv("GARDEN_VERBOSE") == "1" {
		baseLogger = logger.NewConsoleLogger()
	} else {
		baseLogger = &silentLogger{}
	}
	eventBus := events.NewEventBus(baseLogger)

	// 创建 WebSocket Hub
	hub := websocket.NewHub(eventBus)

	// 启动 WebSocket 服务器
	server := websocket.StartWebSocketServer(hub, ":8080")

	// 在单独的 goroutine 中运行 Hub
	go hub.Run()

	// 发送初始状态消息
	hub.SendGardenStatus("🌱 花园正在初始化...")

	// 使用 WaitGroup 来协调 goroutines
	var wg sync.WaitGroup

	// 在单独的 goroutine 中运行花园
	wg.Add(1)
	go func() {
		defer wg.Done()

		// 创建带超时的上下文用于花园运行
		gardenCtx, gardenCancel := context.WithTimeout(ctx, 300*time.Second)
		defer gardenCancel()

		hub.SendGardenStatus("🌸 花园开始运行...")

		out, err := garden.RunGardenWithEventBus(gardenCtx, eventBus)
		if err != nil {
			fmt.Printf("❌ RunGarden error: %v\n", err)
			hub.SendGardenStatus(fmt.Sprintf("❌ 花园运行出错: %v", err))
			return
		}

		fmt.Printf("\n✅ Garden run success: tasks=%d\n\n", len(out.TasksOutput))
		fmt.Println("--- Aggregated Output (Raw) ---")
		fmt.Println(out.Raw)

		hub.SendGardenStatus("✅ 花园运行完成！")

		// 等待一段时间让用户看到结果
		time.Sleep(10 * time.Second)
		hub.SendGardenStatus("🌿 花园进入休眠状态...")
	}()

	// 设置优雅关闭
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号或花园完成
	go func() {
		wg.Wait()
		// 花园运行完成后，继续保持 WebSocket 服务器运行
		fmt.Println("🌱 花园运行完成，WebSocket 服务器继续运行...")
		hub.SendGardenStatus("🌱 花园运行完成，聊天室保持开放")
	}()

	// 等待关闭信号
	<-sigChan
	fmt.Println("\n🛑 收到关闭信号，正在优雅关闭...")

	// 发送关闭消息
	hub.SendGardenStatus("🌙 花园正在关闭...")

	// 关闭 WebSocket Hub
	hub.Shutdown()

	// 关闭 HTTP 服务器
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer shutdownCancel()

	if err := server.Shutdown(shutdownCtx); err != nil {
		log.Printf("HTTP server shutdown error: %v", err)
	}

	fmt.Println("✅ 应用程序已优雅关闭")
}

// silentLogger 实现静默日志记录器
type silentLogger struct{}

func (l *silentLogger) Debug(msg string, fields ...logger.Field) {}
func (l *silentLogger) Info(msg string, fields ...logger.Field)  {}
func (l *silentLogger) Warn(msg string, fields ...logger.Field)  {}
func (l *silentLogger) Error(msg string, fields ...logger.Field) {}
func (l *silentLogger) Fatal(msg string, fields ...logger.Field) {}
func (l *silentLogger) With(fields ...logger.Field) logger.Logger { return l }
