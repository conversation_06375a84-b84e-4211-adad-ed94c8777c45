/* 主题样式 */

/* 暗色主题 */
[data-theme="dark"] {
    --primary-color: #4ade80;
    --secondary-color: #9ca3af;
    --background-color: #111827;
    --surface-color: #1f2937;
    --text-primary: #f9fafb;
    --text-secondary: #9ca3af;
    --border-color: #374151;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.3);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.4);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.5);
    
    /* Agent 颜色 - 暗色主题调整 */
    --rose-color: #f472b6;
    --sunflower-color: #fbbf24;
    --lavender-color: #c084fc;
    --lily-color: #86efac;
    --tulip-color: #f87171;
    --system-color: #9ca3af;
    --garden-color: #4ade80;
}

/* 主题切换动画 */
* {
    transition: background-color var(--transition-medium), 
                color var(--transition-medium), 
                border-color var(--transition-medium);
}

/* 暗色主题特定样式 */
[data-theme="dark"] .welcome-content {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .loading-spinner {
    border-color: var(--border-color);
    border-top-color: var(--primary-color);
}

[data-theme="dark"] .message:hover {
    background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .message.highlighted {
    background: rgba(74, 222, 128, 0.1);
    border-color: var(--primary-color);
}

[data-theme="dark"] .message-type-indicator.start {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

[data-theme="dark"] .message-type-indicator.response {
    background: rgba(74, 222, 128, 0.2);
    color: var(--primary-color);
}

[data-theme="dark"] .message-type-indicator.task-start {
    background: rgba(96, 165, 250, 0.2);
    color: #60a5fa;
}

[data-theme="dark"] .message-type-indicator.task-complete {
    background: rgba(74, 222, 128, 0.2);
    color: var(--primary-color);
}

/* 滚动条暗色主题 */
[data-theme="dark"] .messages-list::-webkit-scrollbar-track {
    background: var(--background-color);
}

[data-theme="dark"] .messages-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
}

[data-theme="dark"] .messages-list::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* 主题切换按钮样式 */
.theme-toggle .theme-icon {
    transition: var(--transition-medium);
}

[data-theme="dark"] .theme-toggle .theme-icon::before {
    content: "☀️";
}

[data-theme="light"] .theme-toggle .theme-icon::before {
    content: "🌙";
}

/* Agent 特定颜色类 */
.agent-rose {
    --agent-color: var(--rose-color);
}

.agent-sunflower {
    --agent-color: var(--sunflower-color);
}

.agent-lavender {
    --agent-color: var(--lavender-color);
}

.agent-lily {
    --agent-color: var(--lily-color);
}

.agent-tulip {
    --agent-color: var(--tulip-color);
}

.agent-system {
    --agent-color: var(--system-color);
}

.agent-garden {
    --agent-color: var(--garden-color);
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
    
    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
    
    .message {
        border-width: 2px;
    }
    
    .agent-card {
        border-width: 2px;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-spinner {
        animation: none;
        border: 2px solid var(--primary-color);
    }
}

/* 打印样式 */
@media print {
    .app-header,
    .agents-panel,
    .system-panel,
    .messages-controls {
        display: none !important;
    }
    
    .app-main {
        grid-template-columns: 1fr !important;
    }
    
    .messages-area {
        height: auto !important;
    }
    
    .messages-container {
        overflow: visible !important;
    }
    
    .message {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

/* 焦点样式 */
.control-btn:focus,
.theme-toggle:focus,
.panel-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.agent-card:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

/* 选择样式 */
::selection {
    background: rgba(40, 167, 69, 0.2);
    color: var(--text-primary);
}

[data-theme="dark"] ::selection {
    background: rgba(74, 222, 128, 0.3);
    color: var(--text-primary);
}

/* 工具提示样式 */
[title] {
    position: relative;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: var(--surface-color);
    padding: 4px 8px;
    border-radius: var(--border-radius-small);
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
    animation: tooltip-appear 0.2s ease-out;
}

@keyframes tooltip-appear {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(4px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}
