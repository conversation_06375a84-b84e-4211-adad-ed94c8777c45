/**
 * WebSocket 连接管理模块
 * 负责与后端 WebSocket 服务器的连接、重连、消息发送和接收
 */

class WebSocketManager {
    constructor() {
        this.ws = null;
        this.url = "ws://localhost:8080/ws";
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
        this.isConnecting = false;
        this.isManualClose = false;
        this.autoReconnect = true; // 是否自动重连

        // 事件监听器
        this.listeners = {
            open: [],
            close: [],
            error: [],
            message: [],
            connecting: []
        };

        // 连接状态
        this.connectionState = 'disconnected'; // disconnected, connecting, connected

        // 不自动连接，等待用户手动连接
        // this.connect();
    }
    

    
    /**
     * 连接到 WebSocket 服务器
     */
    connect() {
        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
            return;
        }
        
        this.isConnecting = true;
        this.connectionState = 'connecting';
        this.emit('connecting');
        
        try {
            this.ws = new WebSocket(this.url);
            this.setupEventListeners();
        } catch (error) {
            console.error('WebSocket connection error:', error);
            this.handleConnectionError(error);
        }
    }
    
    /**
     * 设置 WebSocket 事件监听器
     */
    setupEventListeners() {
        this.ws.onopen = (event) => {
            console.log('WebSocket connected');
            this.isConnecting = false;
            this.reconnectAttempts = 0;
            this.connectionState = 'connected';
            this.emit('open', event);
        };
        
        this.ws.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            this.isConnecting = false;
            this.connectionState = 'disconnected';
            this.emit('close', event);
            
            // 如果不是手动关闭，尝试重连
            if (!this.isManualClose && !event.wasClean) {
                this.scheduleReconnect();
            }
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.handleConnectionError(error);
        };
        
        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.emit('message', data);
            } catch (error) {
                console.error('Failed to parse WebSocket message:', error);
                console.log('Raw message:', event.data);
            }
        };
    }
    
    /**
     * 处理连接错误
     */
    handleConnectionError(error) {
        this.isConnecting = false;
        this.connectionState = 'disconnected';
        this.emit('error', error);
        this.scheduleReconnect();
    }
    
    /**
     * 安排重连
     */
    scheduleReconnect() {
        // 如果手动关闭或禁用自动重连，则不重连
        if (this.isManualClose || !this.autoReconnect) {
            return;
        }

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnect attempts reached');
            this.emit('maxReconnectAttemptsReached');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避

        console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

        setTimeout(() => {
            if (this.connectionState === 'disconnected' && this.autoReconnect) {
                this.connect();
            }
        }, delay);
    }
    
    /**
     * 发送消息
     */
    send(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            try {
                const message = typeof data === 'string' ? data : JSON.stringify(data);
                this.ws.send(message);
                return true;
            } catch (error) {
                console.error('Failed to send WebSocket message:', error);
                return false;
            }
        } else {
            console.warn('WebSocket is not connected');
            return false;
        }
    }
    
    /**
     * 手动关闭连接
     */
    close() {
        this.isManualClose = true;
        if (this.ws) {
            this.ws.close(1000, 'Manual close');
        }
    }
    
    /**
     * 重新连接
     */
    reconnect() {
        this.close();
        this.isManualClose = false;
        this.reconnectAttempts = 0;
        setTimeout(() => {
            this.connect();
        }, 100);
    }
    
    /**
     * 添加事件监听器
     */
    on(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event].push(callback);
        }
    }
    
    /**
     * 移除事件监听器
     */
    off(event, callback) {
        if (this.listeners[event]) {
            const index = this.listeners[event].indexOf(callback);
            if (index > -1) {
                this.listeners[event].splice(index, 1);
            }
        }
    }
    
    /**
     * 触发事件
     */
    emit(event, data) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in ${event} listener:`, error);
                }
            });
        }
    }
    
    /**
     * 获取连接状态
     */
    getConnectionState() {
        return this.connectionState;
    }
    
    /**
     * 检查是否已连接
     */
    isConnected() {
        return this.ws && this.ws.readyState === WebSocket.OPEN;
    }
    
    /**
     * 获取连接信息
     */
    getConnectionInfo() {
        return {
            state: this.connectionState,
            url: this.url,
            reconnectAttempts: this.reconnectAttempts,
            maxReconnectAttempts: this.maxReconnectAttempts,
            readyState: this.ws ? this.ws.readyState : null,
            autoReconnect: this.autoReconnect
        };
    }

    /**
     * 设置自动重连
     */
    setAutoReconnect(enabled) {
        this.autoReconnect = enabled;
    }

    /**
     * 手动连接（用户点击连接按钮）
     */
    manualConnect() {
        this.isManualClose = false;
        this.autoReconnect = true;
        this.reconnectAttempts = 0;
        this.connect();
    }

    /**
     * 手动断开（用户点击断开按钮）
     */
    manualDisconnect() {
        this.isManualClose = true;
        this.autoReconnect = false;
        this.close();
    }

    /**
     * 切换连接状态
     */
    toggleConnection() {
        if (this.isConnected()) {
            this.manualDisconnect();
        } else if (this.connectionState === 'connecting') {
            this.manualDisconnect();
        } else {
            this.manualConnect();
        }
    }
}

// 导出 WebSocketManager 类
window.WebSocketManager = WebSocketManager;
