package main

import (
	"log"
	"os"

	"github.com/joho/godotenv"
	
	"github.com/username/garden-demo/internal/crew"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found")
	}
	
	// 创建并运行crew
	c, err := crew.NewGardenDemoCrew()
	if err != nil {
		log.Fatalf("Failed to create crew: %v", err)
	}
	
	// 运行crew
	if err := c.Run(); err != nil {
		log.Fatalf("Failed to run crew: %v", err)
	}
}
