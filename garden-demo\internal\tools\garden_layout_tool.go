package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	
	"github.com/ynl/greensoulai/internal/agent"
)

// GardenGrid 花园网格
type GardenGrid struct {
	Size    int                `json:"size"`    // 网格大小 (10x10)
	Cells   [][]string         `json:"cells"`   // 每个格子的内容
	Flowers map[string]FlowerPosition `json:"flowers"` // 花卉位置信息
}

// FlowerPosition 花卉位置信息
type FlowerPosition struct {
	Name      string `json:"name"`
	X         int    `json:"x"`
	Y         int    `json:"y"`
	Size      int    `json:"size"`      // 占用格子数
	Placed    bool   `json:"placed"`    // 是否已放置
	Priority  int    `json:"priority"`  // 优先级
}

// NewGardenLayoutTool 创建花园布局工具
func NewGardenLayoutTool() agent.Tool {
	return agent.NewBaseTool(
		"garden_layout",
		"花园布局分析工具，用于分析10x10米花园的空间布局，检查位置可用性，计算距离和光照条件",
		func(ctx context.Context, args map[string]interface{}) (interface{}, error) {
			action, ok := args["action"].(string)
			if !ok {
				return nil, fmt.Errorf("missing action parameter")
			}
			
			switch action {
			case "check_position":
				return checkPosition(args)
			case "suggest_position":
				return suggestPosition(args)
			case "analyze_garden":
				return analyzeGarden(args)
			case "get_available_spaces":
				return getAvailableSpaces(args)
			default:
				return nil, fmt.Errorf("unknown action: %s", action)
			}
		},
	)
}

// checkPosition 检查指定位置是否适合
func checkPosition(args map[string]interface{}) (interface{}, error) {
	x, ok := args["x"].(float64)
	if !ok {
		return nil, fmt.Errorf("missing x coordinate")
	}
	y, ok := args["y"].(float64)
	if !ok {
		return nil, fmt.Errorf("missing y coordinate")
	}
	
	flowerType, ok := args["flower_type"].(string)
	if !ok {
		return nil, fmt.Errorf("missing flower_type")
	}
	
	// 分析位置条件
	analysis := map[string]interface{}{
		"position": fmt.Sprintf("(%d,%d)", int(x), int(y)),
		"available": true,
		"light_condition": analyzeLightCondition(int(x), int(y)),
		"drainage": analyzeDrainage(int(x), int(y)),
		"soil_quality": analyzeSoilQuality(int(x), int(y)),
		"space_available": analyzeSpaceAvailable(int(x), int(y)),
		"recommendations": generateRecommendations(flowerType, int(x), int(y)),
	}
	
	return analysis, nil
}

// suggestPosition 为特定花卉建议位置
func suggestPosition(args map[string]interface{}) (interface{}, error) {
	flowerType, ok := args["flower_type"].(string)
	if !ok {
		return nil, fmt.Errorf("missing flower_type")
	}
	
	existingFlowers := make(map[string]interface{})
	if existing, ok := args["existing_flowers"]; ok {
		existingFlowers = existing.(map[string]interface{})
	}
	
	suggestions := generatePositionSuggestions(flowerType, existingFlowers)
	
	return map[string]interface{}{
		"flower_type": flowerType,
		"suggestions": suggestions,
		"reasoning": generateSuggestionReasoning(flowerType),
	}, nil
}

// analyzeGarden 分析整个花园状态
func analyzeGarden(args map[string]interface{}) (interface{}, error) {
	currentLayout := make(map[string]interface{})
	if layout, ok := args["current_layout"]; ok {
		currentLayout = layout.(map[string]interface{})
	}
	
	analysis := map[string]interface{}{
		"total_area": "100平方米 (10x10米)",
		"zones": map[string]interface{}{
			"north": "部分遮阴，适合百合等半阴植物",
			"south": "全日照，适合向日葵、玫瑰等喜阳植物",
			"east": "晨光充足，适合大多数花卉",
			"west": "午后光照强烈，需要耐热植物",
			"center": "光照条件中等，适合中等需求植物",
		},
		"soil_conditions": map[string]interface{}{
			"north_side": "略湿润，排水中等",
			"south_side": "较干燥，排水良好",
			"east_side": "湿润适中",
			"west_side": "干燥，需要补充水分",
		},
		"space_utilization": calculateSpaceUtilization(currentLayout),
		"recommendations": generateGardenRecommendations(currentLayout),
	}
	
	return analysis, nil
}

// getAvailableSpaces 获取可用空间
func getAvailableSpaces(args map[string]interface{}) (interface{}, error) {
	occupiedSpaces := make(map[string]interface{})
	if occupied, ok := args["occupied_spaces"]; ok {
		occupiedSpaces = occupied.(map[string]interface{})
	}
	
	available := make([]map[string]interface{}, 0)
	
	// 分析10x10网格的可用空间
	for x := 0; x < 10; x++ {
		for y := 0; y < 10; y++ {
			posKey := fmt.Sprintf("%d,%d", x, y)
			if _, occupied := occupiedSpaces[posKey]; !occupied {
				available = append(available, map[string]interface{}{
					"x": x,
					"y": y,
					"position": fmt.Sprintf("(%d,%d)", x, y),
					"light": analyzeLightCondition(x, y),
					"drainage": analyzeDrainage(x, y),
					"size_category": analyzeSpaceSize(x, y),
				})
			}
		}
	}
	
	return map[string]interface{}{
		"available_positions": available,
		"total_available": len(available),
		"recommendations_by_flower": generateFlowerSpecificRecommendations(available),
	}, nil
}

// 辅助函数
func analyzeLightCondition(x, y int) string {
	// 模拟光照条件 - 南侧光照最好
	if y >= 7 {
		return "充足阳光 (8-10小时)"
	} else if y >= 4 {
		return "中等阳光 (6-8小时)"
	} else {
		return "部分阴影 (4-6小时)"
	}
}

func analyzeDrainage(x, y int) string {
	// 模拟排水条件 - 南侧和高处排水更好
	if y >= 6 || x <= 2 || x >= 8 {
		return "排水良好"
	} else if y >= 3 {
		return "排水中等"
	} else {
		return "排水较差，需要改良"
	}
}

func analyzeSoilQuality(x, y int) string {
	// 模拟土壤质量
	if x >= 3 && x <= 7 && y >= 3 && y <= 7 {
		return "肥沃土壤"
	} else if x <= 1 || x >= 9 || y <= 1 || y >= 9 {
		return "贫瘠土壤"
	} else {
		return "中等肥力土壤"
	}
}

func analyzeSpaceAvailable(x, y int) string {
	// 分析可用空间大小
	if x <= 1 || x >= 8 || y <= 1 || y >= 8 {
		return "边缘位置，空间有限"
	} else {
		return "中心区域，空间充足"
	}
}

func analyzeSpaceSize(x, y int) string {
	if x <= 2 || x >= 7 || y <= 2 || y >= 7 {
		return "小空间"
	} else if x <= 4 || x >= 5 || y <= 4 || y >= 5 {
		return "中等空间"
	} else {
		return "大空间"
	}
}

func generateRecommendations(flowerType string, x, y int) []string {
	recommendations := make([]string, 0)
	
	light := analyzeLightCondition(x, y)
	drainage := analyzeDrainage(x, y)
	
	switch flowerType {
	case "sunflower", "向日葵":
		if !strings.Contains(light, "充足") {
			recommendations = append(recommendations, "向日葵需要更多阳光，建议移至南侧")
		}
		if y < 5 {
			recommendations = append(recommendations, "建议放置在后排，避免遮挡其他植物")
		}
	case "lavender", "薰衣草":
		if strings.Contains(drainage, "较差") {
			recommendations = append(recommendations, "薰衣草需要良好排水，建议改良土壤或选择其他位置")
		}
		if x >= 2 && x <= 7 {
			recommendations = append(recommendations, "薰衣草适合边缘种植，可以考虑边界位置")
		}
	case "lily", "百合":
		if strings.Contains(light, "充足") {
			recommendations = append(recommendations, "百合喜欢半阴环境，此位置可能过于阳光")
		}
	case "rose", "玫瑰":
		if !strings.Contains(light, "充足") && !strings.Contains(light, "中等") {
			recommendations = append(recommendations, "玫瑰需要充足光照，建议选择光照更好的位置")
		}
	case "tulip", "郁金香":
		if y >= 6 {
			recommendations = append(recommendations, "郁金香适合前景种植，建议放在前排")
		}
	}
	
	if len(recommendations) == 0 {
		recommendations = append(recommendations, "此位置适合该花卉种植")
	}
	
	return recommendations
}

func generatePositionSuggestions(flowerType string, existingFlowers map[string]interface{}) []map[string]interface{} {
	suggestions := make([]map[string]interface{}, 0)
	
	switch flowerType {
	case "sunflower", "向日葵":
		suggestions = append(suggestions, map[string]interface{}{
			"position": "(2,8)",
			"reasoning": "南侧充足阳光，后排位置不遮挡其他植物",
			"priority": 9,
		})
		suggestions = append(suggestions, map[string]interface{}{
			"position": "(8,8)",
			"reasoning": "南侧边角，充足阳光且空间独立",
			"priority": 8,
		})
		
	case "lavender", "薰衣草":
		suggestions = append(suggestions, map[string]interface{}{
			"position": "(0,5)",
			"reasoning": "西侧边缘，排水良好，可作为天然边界",
			"priority": 9,
		})
		suggestions = append(suggestions, map[string]interface{}{
			"position": "(9,3)",
			"reasoning": "东侧边缘，排水良好，晨光充足",
			"priority": 8,
		})
		
	case "rose", "玫瑰":
		suggestions = append(suggestions, map[string]interface{}{
			"position": "(5,6)",
			"reasoning": "中央偏南，光照充足，有足够空间展示",
			"priority": 9,
		})
		suggestions = append(suggestions, map[string]interface{}{
			"position": "(3,7)",
			"reasoning": "南侧区域，光照良好，适合作为焦点",
			"priority": 8,
		})
		
	case "lily", "百合":
		suggestions = append(suggestions, map[string]interface{}{
			"position": "(4,3)",
			"reasoning": "中央偏北，半阴环境，适合群植",
			"priority": 9,
		})
		suggestions = append(suggestions, map[string]interface{}{
			"position": "(6,2)",
			"reasoning": "北侧区域，光照适中，环境舒适",
			"priority": 8,
		})
		
	case "tulip", "郁金香":
		suggestions = append(suggestions, map[string]interface{}{
			"position": "(3,9)",
			"reasoning": "南侧前排，春季阳光充足，视觉效果好",
			"priority": 9,
		})
		suggestions = append(suggestions, map[string]interface{}{
			"position": "(7,9)",
			"reasoning": "南侧前排，适合前景种植",
			"priority": 8,
		})
	}
	
	return suggestions
}

func generateSuggestionReasoning(flowerType string) string {
	switch flowerType {
	case "sunflower", "向日葵":
		return "向日葵需要充足阳光和较大空间，应放置在南侧后排，避免遮挡其他植物"
	case "lavender", "薰衣草":
		return "薰衣草需要良好排水和适度独立空间，边缘位置最适合"
	case "rose", "玫瑰":
		return "玫瑰作为花园焦点，需要充足光照和展示空间，适合中央区域"
	case "lily", "百合":
		return "百合喜欢半阴环境和群植，适合北侧或有遮挡的区域"
	case "tulip", "郁金香":
		return "郁金香适合前景种植，春季需要阳光，夏季可接受遮阴"
	default:
		return "根据植物特性选择最适合的位置"
	}
}

func calculateSpaceUtilization(currentLayout map[string]interface{}) map[string]interface{} {
	totalCells := 100
	occupiedCells := len(currentLayout)
	
	return map[string]interface{}{
		"total_cells": totalCells,
		"occupied_cells": occupiedCells,
		"available_cells": totalCells - occupiedCells,
		"utilization_rate": fmt.Sprintf("%.1f%%", float64(occupiedCells)/float64(totalCells)*100),
	}
}

func generateGardenRecommendations(currentLayout map[string]interface{}) []string {
	recommendations := []string{
		"建议在南侧种植喜阳植物（向日葵、玫瑰）",
		"北侧适合半阴植物（百合）",
		"边缘位置适合薰衣草等独立植物",
		"前排种植低矮植物（郁金香）避免遮挡",
		"考虑植物的生长季节，合理搭配",
		"预留通道空间便于日常维护",
	}
	
	return recommendations
}

func generateFlowerSpecificRecommendations(available []map[string]interface{}) map[string][]map[string]interface{} {
	recommendations := make(map[string][]map[string]interface{})
	
	for _, pos := range available {
		light := pos["light"].(string)
		drainage := pos["drainage"].(string)
		x := pos["x"].(int)
		y := pos["y"].(int)
		
		// 向日葵推荐
		if strings.Contains(light, "充足") && y >= 6 {
			if recommendations["向日葵"] == nil {
				recommendations["向日葵"] = make([]map[string]interface{}, 0)
			}
			recommendations["向日葵"] = append(recommendations["向日葵"], pos)
		}
		
		// 薰衣草推荐
		if strings.Contains(drainage, "良好") && (x <= 1 || x >= 8 || y <= 1 || y >= 8) {
			if recommendations["薰衣草"] == nil {
				recommendations["薰衣草"] = make([]map[string]interface{}, 0)
			}
			recommendations["薰衣草"] = append(recommendations["薰衣草"], pos)
		}
		
		// 玫瑰推荐
		if (strings.Contains(light, "充足") || strings.Contains(light, "中等")) && x >= 3 && x <= 7 {
			if recommendations["玫瑰"] == nil {
				recommendations["玫瑰"] = make([]map[string]interface{}, 0)
			}
			recommendations["玫瑰"] = append(recommendations["玫瑰"], pos)
		}
		
		// 百合推荐
		if strings.Contains(light, "部分") || strings.Contains(light, "中等") {
			if recommendations["百合"] == nil {
				recommendations["百合"] = make([]map[string]interface{}, 0)
			}
			recommendations["百合"] = append(recommendations["百合"], pos)
		}
		
		// 郁金香推荐
		if y >= 7 && x >= 2 && x <= 8 {
			if recommendations["郁金香"] == nil {
				recommendations["郁金香"] = make([]map[string]interface{}, 0)
			}
			recommendations["郁金香"] = append(recommendations["郁金香"], pos)
		}
	}
	
	return recommendations
}
