# garden-demo

garden-demo crew project

## 🚀 快速开始

### 1. 环境准备

确保你已安装Go 1.21或更高版本。

### 2. 安装依赖

```bash
go mod download
```

### 3. 配置环境变量

复制 `.env.example` 到 `.env` 并设置你的API密钥：

```bash
cp .env.example .env
# 编辑 .env 文件，设置 OPENAI_API_KEY
```

### 4. 运行项目

```bash
# 使用 greensoulai CLI
greensoulai run

# 或直接运行
go run cmd/main.go

# 或使用 Makefile
make run
```

## 📁 项目结构

```
garden-demo/
├── cmd/
│   └── main.go              # 程序入口
├── internal/
│   ├── agents/              # 智能体定义
│   ├── tasks/               # 任务定义
│   ├── tools/               # 工具实现
│   └── crew/                # 团队配置
├── config/                  # 配置文件
├── docs/                    # 文档
├── scripts/                 # 脚本文件
├── greensoulai.yaml         # 项目配置
├── go.mod                   # Go模块文件
├── .env                     # 环境变量
├── Makefile                 # 构建脚本
└── README.md               # 说明文档
```

## 🤖 智能体配置

本项目包含以下智能体：

- **researcher** (高级研究员)
  - 目标: 进行深入的研究和分析
  - 背景: 你是一位经验丰富的研究专家，擅长收集、分析和总结信息。
  - 工具: search_tool, analysis_tool

## 📋 任务配置

定义的任务：

- **research_task**
  - 描述: 进行主题研究
  - 期望输出: 详细的研究报告
  - 分配智能体: researcher

## 🛠️ 工具集

可用工具：

- search_tool
- analysis_tool

## ⚙️ 配置说明

主要配置文件：

- `greensoulai.yaml`: 项目主配置
- `.env`: 环境变量配置

### LLM 配置

当前使用的LLM配置：
- 提供商: openai
- 模型: gpt-4o-mini
- 温度: 0.70

## 🔧 自定义开发

### 添加新的智能体

1. 在 `internal/agents/` 中创建新的智能体文件
2. 实现智能体逻辑
3. 在 `greensoulai.yaml` 中配置智能体

### 添加新的任务

1. 在 `internal/tasks/` 中创建新的任务文件
2. 实现任务逻辑
3. 在 `greensoulai.yaml` 中配置任务

### 添加新的工具

1. 在 `internal/tools/` 中创建新的工具文件
2. 实现工具逻辑
3. 在智能体配置中引用工具

## 📚 文档

- [API文档](docs/api.md)
- [开发指南](docs/development.md)
- [部署指南](docs/deployment.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

本项目采用 MIT 许可证。
