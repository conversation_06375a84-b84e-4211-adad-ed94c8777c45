package websocket

import (
	"context"
	"encoding/json"
	"log"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/ynl/greensoulai/pkg/events"
)

// Hub 管理所有 WebSocket 连接和消息广播
type Hub struct {
	// 注册的客户端连接
	clients map[*Client]bool

	// 从客户端接收的消息
	broadcast chan []byte

	// 注册客户端连接
	register chan *Client

	// 注销客户端连接
	unregister chan *Client

	// 事件总线，用于监听智能体事件
	eventBus events.EventBus

	// 互斥锁保护并发访问
	mu sync.RWMutex

	// 上下文用于优雅关闭
	ctx context.Context
	cancel context.CancelFunc
}

// Client 代表一个 WebSocket 客户端连接
type Client struct {
	// WebSocket 连接
	conn *websocket.Conn

	// 发送消息的缓冲通道
	send chan []byte

	// 客户端 ID
	id string

	// 连接时间
	connectedAt time.Time
}

// AgentMessage 发送给前端的智能体消息格式
type AgentMessage struct {
	Type      string    `json:"type"`
	Timestamp time.Time `json:"timestamp"`
	Agent     AgentInfo `json:"agent"`
	Message   MessageInfo `json:"message"`
}

// AgentInfo 智能体信息
type AgentInfo struct {
	Name   string `json:"name"`
	Role   string `json:"role"`
	Avatar string `json:"avatar"`
	Color  string `json:"color"`
}

// MessageInfo 消息信息
type MessageInfo struct {
	Content         string `json:"content"`
	MessageType     string `json:"messageType"` // "start", "response", "complete"
	TaskDescription string `json:"taskDescription"`
}

// NewHub 创建新的 Hub
func NewHub(eventBus events.EventBus) *Hub {
	ctx, cancel := context.WithCancel(context.Background())
	
	hub := &Hub{
		clients:    make(map[*Client]bool),
		broadcast:  make(chan []byte, 256),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		eventBus:   eventBus,
		ctx:        ctx,
		cancel:     cancel,
	}

	// 注册事件监听器
	hub.setupEventListeners()

	return hub
}

// Run 启动 Hub，处理客户端连接和消息广播
func (h *Hub) Run() {
	defer h.cancel()

	for {
		select {
		case <-h.ctx.Done():
			log.Println("Hub shutting down...")
			return

		case client := <-h.register:
			h.mu.Lock()
			h.clients[client] = true
			h.mu.Unlock()
			
			log.Printf("Client %s connected. Total clients: %d", client.id, len(h.clients))
			
			// 发送欢迎消息
			welcomeMsg := AgentMessage{
				Type:      "system_message",
				Timestamp: time.Now(),
				Agent: AgentInfo{
					Name:   "System",
					Role:   "System",
					Avatar: "/assets/avatars/system.svg",
					Color:  "#6c757d",
				},
				Message: MessageInfo{
					Content:     "欢迎来到花园智能体聊天室！",
					MessageType: "info",
				},
			}
			
			if data, err := json.Marshal(welcomeMsg); err == nil {
				select {
				case client.send <- data:
				default:
					h.closeClient(client)
				}
			}

		case client := <-h.unregister:
			h.mu.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
				log.Printf("Client %s disconnected. Total clients: %d", client.id, len(h.clients))
			}
			h.mu.Unlock()

		case message := <-h.broadcast:
			h.mu.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					h.closeClient(client)
				}
			}
			h.mu.RUnlock()
		}
	}
}

// closeClient 关闭客户端连接
func (h *Hub) closeClient(client *Client) {
	h.mu.Lock()
	defer h.mu.Unlock()
	
	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		close(client.send)
		client.conn.Close()
	}
}

// Shutdown 优雅关闭 Hub
func (h *Hub) Shutdown() {
	h.cancel()
	
	h.mu.Lock()
	defer h.mu.Unlock()
	
	for client := range h.clients {
		close(client.send)
		client.conn.Close()
	}
}

// BroadcastMessage 广播消息给所有客户端
func (h *Hub) BroadcastMessage(message AgentMessage) {
	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("Error marshaling message: %v", err)
		return
	}

	select {
	case h.broadcast <- data:
	default:
		log.Println("Broadcast channel is full, dropping message")
	}
}

// GetClientCount 获取当前连接的客户端数量
func (h *Hub) GetClientCount() int {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return len(h.clients)
}
