# GreenSoulAI 花园聊天界面部署指南

本指南将帮助您部署和测试 GreenSoulAI Garden 的实时聊天界面。

## 🎯 概述

我们为 `examples/garden` 创建了一个完整的实时聊天界面，包括：

- **实时 WebSocket 通信**: 观看智能体之间的实时对话
- **现代化 UI**: 美观的聊天界面，支持暗色/亮色主题
- **多智能体支持**: Rose、Sunflower、Lavender、Lily、Tulip 五个智能体
- **状态监控**: 连接状态、系统统计、运行时间等
- **响应式设计**: 支持桌面和移动设备

## 🚀 快速部署

### 1. 准备环境

确保您已经：
- 安装了 Go 1.21+
- 获得了 OpenRouter API 密钥（免费）

### 2. 设置 API 密钥

```bash
# 获取免费 API 密钥: https://openrouter.ai/
export OPENROUTER_API_KEY="your-openrouter-api-key-here"
```

### 3. 启动服务

```bash
# 进入 garden 目录
cd examples/garden

# 启动服务（包含 WebSocket 服务器和静态文件服务）
go run ./cmd
```

### 4. 访问聊天界面

服务启动后，打开浏览器访问：
```
http://localhost:8080
```

## 🧪 测试步骤

### 1. 基础连接测试

```bash
# 在另一个终端中运行 WebSocket 测试工具
cd examples/garden
go run tools/test-websocket.go
```

预期输出：
```
🧪 GreenSoulAI Garden WebSocket 测试工具
连接到: ws://localhost:8080/ws
✅ WebSocket 连接成功！
🔄 开始监听消息... (按 Ctrl+C 退出)
```

### 2. 前端界面测试

1. **连接状态检查**:
   - 顶部应显示 "已连接" 状态
   - 连接指示器应为绿色

2. **智能体面板**:
   - 左侧应显示 5 个智能体卡片
   - 每个智能体都有对应的颜色和头像

3. **消息接收**:
   - 当 Garden 运行时，应能看到实时消息
   - 消息应按时间顺序显示
   - 不同类型的消息有不同的样式

### 3. 功能测试

#### 主题切换
- 点击右上角的主题切换按钮
- 界面应在亮色/暗色主题间切换

#### 消息交互
- 点击智能体卡片应高亮该智能体的消息
- 使用 `Ctrl+K` 清空消息
- 使用 `End` 键滚动到底部

#### 系统信息
- 右侧面板应显示在线用户数
- 运行时间应实时更新
- 系统日志应记录连接事件

## 📁 文件结构

```
examples/garden/
├── cmd/
│   └── garden_main.go          # 主程序（已修改，集成 WebSocket）
├── websocket/                  # WebSocket 服务器代码
│   ├── hub.go                 # WebSocket Hub 管理
│   ├── server.go              # HTTP/WebSocket 服务器
│   └── events.go              # 事件处理
├── web/                       # 前端文件
│   ├── index.html             # 主页面
│   ├── css/                   # 样式文件
│   ├── js/                    # JavaScript 文件
│   └── assets/                # 静态资源
├── tools/
│   └── test-websocket.go      # WebSocket 测试工具
└── CHAT_INTERFACE_GUIDE.md    # 本指南
```

## 🔧 配置选项

### 环境变量

```bash
# 必需
OPENROUTER_API_KEY="your-api-key"

# 可选
GARDEN_MODEL="moonshotai/kimi-k2:free"  # 默认模型
GARDEN_VERBOSE="1"                      # 启用详细日志
GARDEN_DELAY_MS="300"                   # 对话延迟（毫秒）
GARDEN_DELAY_JITTER_PCT="30"            # 延迟抖动百分比
```

### 服务器配置

默认配置：
- WebSocket 端口: `:8080`
- 静态文件目录: `./web/`
- 最大消息大小: 512 字节
- 连接超时: 60 秒

## 🌐 API 端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 静态文件服务 |
| `/ws` | WebSocket | WebSocket 连接 |
| `/health` | GET | 健康检查 |
| `/api/stats` | GET | 系统统计信息 |

## 🐛 故障排除

### 常见问题

1. **连接失败**
   ```
   WebSocket connection failed
   ```
   - 检查后端服务是否运行
   - 确认端口 8080 未被占用
   - 检查防火墙设置

2. **API 密钥错误**
   ```
   no API key found, set OPENROUTER_API_KEY
   ```
   - 确保设置了正确的环境变量
   - 检查 API 密钥是否有效

3. **静态文件 404**
   ```
   404 Not Found
   ```
   - 确保 `web/` 目录存在
   - 检查文件路径是否正确

### 调试工具

在浏览器控制台中使用：

```javascript
// 查看应用状态
window.GardenChatDebug.getStatus()

// 手动重连
window.GardenChatDebug.reconnect()

// 导出聊天记录
window.GardenChatDebug.exportChat()
```

## 🚀 部署到生产环境

### 1. 构建优化

```bash
# 构建二进制文件
go build -o garden-server ./cmd

# 运行
OPENROUTER_API_KEY=your-key ./garden-server
```

### 2. 使用反向代理

```nginx
# Nginx 配置示例
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 使用 Docker

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o garden-server ./examples/garden/cmd

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/garden-server .
COPY --from=builder /app/web ./web/
EXPOSE 8080
CMD ["./garden-server"]
```

## 📊 性能监控

### 关键指标

- WebSocket 连接数
- 消息吞吐量
- 内存使用情况
- 响应时间

### 监控端点

```bash
# 健康检查
curl http://localhost:8080/health

# 系统统计
curl http://localhost:8080/api/stats
```

## 🎉 完成！

现在您已经成功部署了 GreenSoulAI Garden 的实时聊天界面！

享受观看智能体们的精彩对话吧！ 🌸🌻💜🌸🌷
