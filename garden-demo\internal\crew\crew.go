package crew

import (
	"context"
	"fmt"
	"os"
	
	"github.com/ynl/greensoulai/internal/crew"
	"github.com/ynl/greensoulai/internal/llm"
	"github.com/ynl/greensoulai/pkg/events"
	"github.com/ynl/greensoulai/pkg/logger"
	"github.com/username/garden-demo/internal/agents"
	"github.com/username/garden-demo/internal/tasks"
)

// GardenDemoCrew 结构
type GardenDemoCrew struct {
	crew crew.Crew
	log  logger.Logger
}

// NewGardenDemoCrew 创建garden-demo团队
func NewGardenDemoCrew() (*GardenDemoCrew, error) {
	// 创建日志器
	log := logger.NewConsoleLogger()
	
	// 创建事件总线
	eventBus := events.NewEventBus(log)
	
	// 创建LLM提供商
	apiKey := os.Getenv("OPENAI_API_KEY")
	if apiKey == "" {
		return nil, fmt.Errorf("OPENAI_API_KEY environment variable is required")
	}
	
	llmProvider := llm.NewOpenAILLM("gpt-4o-mini", llm.WithAPIKey(apiKey))
	
	// 创建Agents
	researcherAgent, err := agents.NewResearcherAgent(llmProvider, eventBus, log)
	if err != nil {
		return nil, fmt.Errorf("failed to create researcher agent: %w", err)
	}
	
	// 创建Tasks
	research_taskTask, err := tasks.NewResearchTaskTask(eventBus, log)
	if err != nil {
		return nil, fmt.Errorf("failed to create research_task task: %w", err)
	}
	
	// 分配任务给Agent
	if err := research_taskTask.SetAssignedAgent(researcherAgent); err != nil {
		return nil, fmt.Errorf("failed to assign task to agent: %w", err)
	}
	
	// 创建Crew配置
	config := &crew.CrewConfig{
		Name:    "garden-demo",
		Process: crew.ProcessSequential,
		Verbose: true,
	}
	
	// 创建Crew
	c, err := crew.NewBaseCrew(config, eventBus, log)
	if err != nil {
		return nil, fmt.Errorf("failed to create crew: %w", err)
	}
	
	// 添加Agents
	agents := []agent.Agent{researcherAgent}
	for _, a := range agents {
		if err := c.AddAgent(a); err != nil {
			return nil, fmt.Errorf("failed to add agent: %w", err)
		}
	}
	
	// 添加Tasks
	tasks := []agent.Task{research_taskTask}
	for _, t := range tasks {
		if err := c.AddTask(t); err != nil {
			return nil, fmt.Errorf("failed to add task: %w", err)
		}
	}
	
	return &GardenDemoCrew{
		crew: c,
		log:  log,
	}, nil
}

// Run 运行Crew
func (c *GardenDemoCrew) Run() error {
	c.log.Info("启动garden-demo团队...")
	
	ctx := context.Background()
	inputs := make(map[string]interface{})
	
	output, err := c.crew.Kickoff(ctx, inputs)
	if err != nil {
		return fmt.Errorf("crew execution failed: %w", err)
	}
	
	c.log.Info("团队执行完成")
	c.log.Info("执行结果", logger.Field{Key: "output", Value: output.Raw})
	
	return nil
}
