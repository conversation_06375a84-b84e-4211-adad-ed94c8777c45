package agents

import (
	"fmt"
	"time"
	
	"github.com/ynl/greensoulai/internal/agent"
	"github.com/ynl/greensoulai/internal/llm"
	"github.com/ynl/greensoulai/pkg/events"
	"github.com/ynl/greensoulai/pkg/logger"
	"github.com/username/garden-demo/internal/tools"
)

// FlowerProfile 花卉基本信息
type FlowerProfile struct {
	Name        string // 花卉名称
	ChineseName string // 中文名称
	LightNeeds  string // 光照需求
	WaterNeeds  string // 排水需求
	SoilNeeds   string // 土壤需求
	SeasonInfo  string // 季节性信息
	SpaceNeeds  string // 空间需求
	Personality string // 性格特征
}

// 花卉配置信息
var flowerProfiles = map[string]FlowerProfile{
	"rose": {
		Name:        "Rose",
		ChineseName: "玫瑰",
		LightNeeds:  "充足阳光，每天至少6小时直射光",
		WaterNeeds:  "排水良好，不耐涝，喜湿润但不积水",
		SoilNeeds:   "肥沃、疏松的微酸性土壤(pH 6.0-6.5)",
		SeasonInfo:  "春夏开花，秋季休眠，需要冬季修剪",
		SpaceNeeds:  "需要1.5-2米间距，垂直空间较大",
		Personality: "优雅高贵，有领导气质，但对环境要求严格",
	},
	"sunflower": {
		Name:        "Sunflower",
		ChineseName: "向日葵",
		LightNeeds:  "极度喜阳，需要全日照(8-10小时)",
		WaterNeeds:  "耐旱但生长期需要充足水分，排水要好",
		SoilNeeds:   "适应性强，偏好中性至弱碱性土壤(pH 6.0-7.5)",
		SeasonInfo:  "夏季开花，生长期短但快速，秋季收获种子",
		SpaceNeeds:  "高大植株，需要2-3米间距，不要遮挡其他植物",
		Personality: "阳光开朗，适应力强，但容易遮挡其他植物",
	},
	"lavender": {
		Name:        "Lavender",
		ChineseName: "薰衣草",
		LightNeeds:  "充足阳光，至少6小时直射光",
		WaterNeeds:  "极度需要排水良好，耐旱，怕积水",
		SoilNeeds:   "贫瘠、石灰质土壤，偏碱性(pH 7.0-8.0)",
		SeasonInfo:  "夏季开花，四季常绿，冬季需要保护",
		SpaceNeeds:  "低矮植株，1米间距，适合边缘种植",
		Personality: "独立自主，喜欢安静角落，有天然驱虫能力",
	},
	"lily": {
		Name:        "Lily",
		ChineseName: "百合",
		LightNeeds:  "半阴半阳，morning sun + afternoon shade最佳",
		WaterNeeds:  "喜湿润但排水良好，根部不能积水",
		SoilNeeds:   "富含有机质的酸性土壤(pH 5.5-6.5)",
		SeasonInfo:  "春夏开花，有球茎越冬，需要冷春化",
		SpaceNeeds:  "中等高度，1-1.5米间距，群植效果好",
		Personality: "温和优雅，喜欢群体生活，需要一定隐私空间",
	},
	"tulip": {
		Name:        "Tulip",
		ChineseName: "郁金香",
		LightNeeds:  "春季充足阳光，夏季休眠期可遮阴",
		WaterNeeds:  "春季需水，夏季休眠期要干燥",
		SoilNeeds:   "疏松肥沃的中性土壤(pH 6.5-7.0)",
		SeasonInfo:  "早春开花，夏季休眠，需要低温春化",
		SpaceNeeds:  "低矮植株，0.5-1米间距，适合前景种植",
		Personality: "季节性很强，春季活跃夏季沉默，需要规律的生活节奏",
	},
}

// NewFlowerAgent 创建花卉智能体
func NewFlowerAgent(flowerType string, llmProvider llm.LLM, eventBus events.EventBus, log logger.Logger) (agent.Agent, error) {
	profile, exists := flowerProfiles[flowerType]
	if !exists {
		return nil, fmt.Errorf("未知的花卉类型: %s", flowerType)
	}
	
	config := agent.AgentConfig{
		Role: fmt.Sprintf("%s (%s)", profile.ChineseName, profile.Name),
		Goal: "在10x10米花园中找到最适合自己的生长位置，与其他花卉和谐共存",
		Backstory: fmt.Sprintf(`你是一株%s，具有以下特性：
性格：%s
光照需求：%s
水分需求：%s
土壤需求：%s
季节特性：%s
空间需求：%s

你需要在协商中：
1. 清楚表达自己的需求和限制
2. 倾听其他花卉的需求
3. 寻找互利共赢的解决方案
4. 为整个花园的和谐做出适当妥协`,
			profile.ChineseName,
			profile.Personality,
			profile.LightNeeds,
			profile.WaterNeeds,
			profile.SoilNeeds,
			profile.SeasonInfo,
			profile.SpaceNeeds),
		LLM:      llmProvider,
		EventBus: eventBus,
		Logger:   log,
		ExecutionConfig: agent.ExecutionConfig{
			MaxIterations:   10,
			Timeout:        15 * time.Minute,
			VerboseLogging: true,
		},
	}
	
	a, err := agent.NewBaseAgent(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create %s agent: %w", profile.ChineseName, err)
	}
	
	// 添加花园布局工具
	flowerTools := []agent.Tool{
		tools.NewGardenLayoutTool(),
		tools.NewNeighborAnalysisTool(),
	}
	
	for _, tool := range flowerTools {
		if err := a.AddTool(tool); err != nil {
			return nil, fmt.Errorf("failed to add tool to %s: %w", profile.ChineseName, err)
		}
	}
	
	return a, nil
}

// NewRoseAgent 创建玫瑰智能体
func NewRoseAgent(llmProvider llm.LLM, eventBus events.EventBus, log logger.Logger) (agent.Agent, error) {
	return NewFlowerAgent("rose", llmProvider, eventBus, log)
}

// NewSunflowerAgent 创建向日葵智能体
func NewSunflowerAgent(llmProvider llm.LLM, eventBus events.EventBus, log logger.Logger) (agent.Agent, error) {
	return NewFlowerAgent("sunflower", llmProvider, eventBus, log)
}

// NewLavenderAgent 创建薰衣草智能体
func NewLavenderAgent(llmProvider llm.LLM, eventBus events.EventBus, log logger.Logger) (agent.Agent, error) {
	return NewFlowerAgent("lavender", llmProvider, eventBus, log)
}

// NewLilyAgent 创建百合智能体
func NewLilyAgent(llmProvider llm.LLM, eventBus events.EventBus, log logger.Logger) (agent.Agent, error) {
	return NewFlowerAgent("lily", llmProvider, eventBus, log)
}

// NewTulipAgent 创建郁金香智能体
func NewTulipAgent(llmProvider llm.LLM, eventBus events.EventBus, log logger.Logger) (agent.Agent, error) {
	return NewFlowerAgent("tulip", llmProvider, eventBus, log)
}
