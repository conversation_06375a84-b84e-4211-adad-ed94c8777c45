package websocket

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

const (
	// 写入消息的超时时间
	writeWait = 10 * time.Second

	// 读取下一个 pong 消息的超时时间
	pongWait = 60 * time.Second

	// 发送 ping 的周期，必须小于 pongWait
	pingPeriod = (pongWait * 9) / 10

	// 允许的最大消息大小
	maxMessageSize = 512
)

var (
	newline = []byte{'\n'}
	space   = []byte{' '}
)

// upgrader 用于升级 HTTP 连接为 WebSocket 连接
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源的连接（生产环境中应该更严格）
		return true
	},
}

// 服务器启动时间
var serverStartTime = time.Now()

// ServeWS 处理 WebSocket 连接请求
func ServeWS(hub *Hub, w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}

	// 创建新的客户端
	client := &Client{
		conn:        conn,
		send:        make(chan []byte, 256),
		id:          uuid.New().String(),
		connectedAt: time.Now(),
	}

	// 注册客户端到 hub
	hub.register <- client

	// 启动客户端的读写 goroutines
	go client.writePump(hub)
	go client.readPump(hub)
}

// readPump 从 WebSocket 连接读取消息
func (c *Client) readPump(hub *Hub) {
	defer func() {
		hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		// 处理客户端发送的消息（如果需要的话）
		log.Printf("Received message from client %s: %s", c.id, string(message))
	}
}

// writePump 向 WebSocket 连接写入消息
func (c *Client) writePump(hub *Hub) {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// Hub 关闭了发送通道
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 将队列中的其他消息一起发送
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write(newline)
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// StartWebSocketServer 启动 WebSocket 服务器
func StartWebSocketServer(hub *Hub, addr string) *http.Server {
	mux := http.NewServeMux()

	// WebSocket 端点
	mux.HandleFunc("/ws", func(w http.ResponseWriter, r *http.Request) {
		ServeWS(hub, w, r)
	})

	// 静态文件服务
	mux.Handle("/", http.FileServer(http.Dir("./web/")))

	// 健康检查端点
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(fmt.Sprintf(`{"status":"ok","clients":%d}`, hub.GetClientCount())))
	})

	// 统计信息端点
	mux.HandleFunc("/api/stats", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("Access-Control-Allow-Origin", "*")
		
		uptime := time.Since(serverStartTime)
		stats := map[string]interface{}{
			"clients": hub.GetClientCount(),
			"uptime": uptime.String(),
			"uptimeSeconds": int(uptime.Seconds()),
			"status": "running",
			"version": "1.0.0",
			"startTime": serverStartTime.Format(time.RFC3339),
		}
		
		if data, err := json.Marshal(stats); err == nil {
			w.Write(data)
		} else {
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(`{"error":"failed to marshal stats"}`))
		}
	})

	server := &http.Server{
		Addr:    addr,
		Handler: mux,
	}

	go func() {
		log.Printf("WebSocket server starting on %s", addr)
		log.Printf("Chat interface available at: http://localhost%s", addr)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("WebSocket server error: %v", err)
		}
	}()

	return server
}

// GetConnectionInfo 获取客户端连接信息
func (c *Client) GetConnectionInfo() map[string]interface{} {
	return map[string]interface{}{
		"id":           c.id,
		"connected_at": c.connectedAt,
		"duration":     time.Since(c.connectedAt).String(),
	}
}

// SendMessage 向特定客户端发送消息
func (c *Client) SendMessage(message []byte) error {
	select {
	case c.send <- message:
		return nil
	default:
		return websocket.ErrCloseSent
	}
}
