package tools

import (
	"context"
	"fmt"
	"math"
	
	"github.com/ynl/greensoulai/internal/agent"
)

// NewNeighborAnalysisTool 创建邻居分析工具
func NewNeighborAnalysisTool() agent.Tool {
	return agent.NewBaseTool(
		"neighbor_analysis",
		"分析花卉之间的相互影响，包括光照遮挡、根系竞争、伴生关系等",
		func(ctx context.Context, args map[string]interface{}) (interface{}, error) {
			action, ok := args["action"].(string)
			if !ok {
				return nil, fmt.Errorf("missing action parameter")
			}
			
			switch action {
			case "analyze_compatibility":
				return analyzeCompatibility(args)
			case "check_spacing":
				return checkSpacing(args)
			case "evaluate_interactions":
				return evaluateInteractions(args)
			case "suggest_companions":
				return suggestCompanions(args)
			default:
				return nil, fmt.Errorf("unknown action: %s", action)
			}
		},
	)
}

// analyzeCompatibility 分析两种花卉的兼容性
func analyzeCompatibility(args map[string]interface{}) (interface{}, error) {
	flower1, ok := args["flower1"].(string)
	if !ok {
		return nil, fmt.E<PERSON><PERSON>("missing flower1 parameter")
	}
	flower2, ok := args["flower2"].(string)
	if !ok {
		return nil, fmt.Errorf("missing flower2 parameter")
	}
	
	compatibility := calculateCompatibility(flower1, flower2)
	
	return map[string]interface{}{
		"flower1": flower1,
		"flower2": flower2,
		"compatibility_score": compatibility.Score,
		"compatibility_level": compatibility.Level,
		"benefits": compatibility.Benefits,
		"conflicts": compatibility.Conflicts,
		"recommendations": compatibility.Recommendations,
	}, nil
}

// checkSpacing 检查两个花卉之间的距离是否合适
func checkSpacing(args map[string]interface{}) (interface{}, error) {
	flower1, ok := args["flower1"].(string)
	if !ok {
		return nil, fmt.Errorf("missing flower1 parameter")
	}
	flower2, ok := args["flower2"].(string)
	if !ok {
		return nil, fmt.Errorf("missing flower2 parameter")
	}
	
	x1, ok := args["x1"].(float64)
	if !ok {
		return nil, fmt.Errorf("missing x1 coordinate")
	}
	y1, ok := args["y1"].(float64)
	if !ok {
		return nil, fmt.Errorf("missing y1 coordinate")
	}
	x2, ok := args["x2"].(float64)
	if !ok {
		return nil, fmt.Errorf("missing x2 coordinate")
	}
	y2, ok := args["y2"].(float64)
	if !ok {
		return nil, fmt.Errorf("missing y2 coordinate")
	}
	
	distance := math.Sqrt(math.Pow(x2-x1, 2) + math.Pow(y2-y1, 2))
	spacing := analyzeSpacing(flower1, flower2, distance)
	
	return map[string]interface{}{
		"flower1": flower1,
		"flower2": flower2,
		"distance": fmt.Sprintf("%.1f米", distance),
		"recommended_distance": spacing.RecommendedDistance,
		"current_status": spacing.Status,
		"impact_analysis": spacing.Impact,
		"suggestions": spacing.Suggestions,
	}, nil
}

// evaluateInteractions 评估多个花卉之间的相互作用
func evaluateInteractions(args map[string]interface{}) (interface{}, error) {
	flowers, ok := args["flowers"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("missing flowers parameter")
	}
	
	interactions := make([]map[string]interface{}, 0)
	
	// 分析所有花卉对之间的相互作用
	for i := 0; i < len(flowers); i++ {
		for j := i + 1; j < len(flowers); j++ {
			flower1 := flowers[i].(map[string]interface{})
			flower2 := flowers[j].(map[string]interface{})
			
			interaction := analyzeInteraction(flower1, flower2)
			interactions = append(interactions, interaction)
		}
	}
	
	overallAssessment := assessOverallHarmony(interactions)
	
	return map[string]interface{}{
		"total_flowers": len(flowers),
		"interactions": interactions,
		"overall_harmony": overallAssessment.HarmonyLevel,
		"harmony_score": overallAssessment.Score,
		"major_issues": overallAssessment.Issues,
		"optimization_suggestions": overallAssessment.Suggestions,
	}, nil
}

// suggestCompanions 为指定花卉建议伴生植物
func suggestCompanions(args map[string]interface{}) (interface{}, error) {
	targetFlower, ok := args["target_flower"].(string)
	if !ok {
		return nil, fmt.Errorf("missing target_flower parameter")
	}
	
	availableFlowers := []string{"rose", "sunflower", "lavender", "lily", "tulip"}
	companions := make([]map[string]interface{}, 0)
	
	for _, flower := range availableFlowers {
		if flower != targetFlower {
			compatibility := calculateCompatibility(targetFlower, flower)
			if compatibility.Score >= 7 { // 只推荐兼容性较高的
				companions = append(companions, map[string]interface{}{
					"flower": flower,
					"compatibility_score": compatibility.Score,
					"benefits": compatibility.Benefits,
					"recommended_distance": getRecommendedDistance(targetFlower, flower),
				})
			}
		}
	}
	
	return map[string]interface{}{
		"target_flower": targetFlower,
		"recommended_companions": companions,
		"planting_tips": getPlantingTips(targetFlower),
	}, nil
}

// 兼容性结构
type CompatibilityResult struct {
	Score           int      `json:"score"`           // 1-10分
	Level           string   `json:"level"`           // 兼容性级别
	Benefits        []string `json:"benefits"`        // 互利之处
	Conflicts       []string `json:"conflicts"`       // 冲突之处
	Recommendations []string `json:"recommendations"` // 建议
}

// 距离分析结构
type SpacingResult struct {
	RecommendedDistance string   `json:"recommended_distance"`
	Status              string   `json:"status"`
	Impact              []string `json:"impact"`
	Suggestions         []string `json:"suggestions"`
}

// 整体和谐评估结构
type HarmonyAssessment struct {
	HarmonyLevel string   `json:"harmony_level"`
	Score        int      `json:"score"`
	Issues       []string `json:"issues"`
	Suggestions  []string `json:"suggestions"`
}

// calculateCompatibility 计算两种花卉的兼容性
func calculateCompatibility(flower1, flower2 string) CompatibilityResult {
	// 花卉兼容性矩阵
	compatibilityMatrix := map[string]map[string]CompatibilityResult{
		"rose": {
			"sunflower": {
				Score: 6,
				Level: "中等兼容",
				Benefits: []string{"向日葵的高度不会过度遮挡玫瑰", "两者都喜欢充足阳光"},
				Conflicts: []string{"向日葵可能在某些时段遮挡玫瑰", "根系可能有轻微竞争"},
				Recommendations: []string{"保持2-3米距离", "向日葵种在玫瑰北侧"},
			},
			"lavender": {
				Score: 9,
				Level: "高度兼容",
				Benefits: []string{"薰衣草驱虫保护玫瑰", "排水需求相似", "都喜欢充足阳光", "香味互补"},
				Conflicts: []string{},
				Recommendations: []string{"可以近距离种植", "薰衣草作为玫瑰的护卫植物"},
			},
			"lily": {
				Score: 7,
				Level: "良好兼容",
				Benefits: []string{"百合的半阴需求与玫瑰的遮阴提供匹配", "开花时间互补"},
				Conflicts: []string{"水分需求略有差异"},
				Recommendations: []string{"百合可种在玫瑰东侧或北侧", "保持1.5米距离"},
			},
			"tulip": {
				Score: 8,
				Level: "良好兼容",
				Benefits: []string{"郁金香春季开花，玫瑰夏季开花，时间互补", "郁金香低矮不影响玫瑰"},
				Conflicts: []string{"夏季休眠期的郁金香需要较干燥环境"},
				Recommendations: []string{"郁金香种在玫瑰前方", "注意夏季浇水控制"},
			},
		},
		"sunflower": {
			"rose": {
				Score: 6,
				Level: "中等兼容",
				Benefits: []string{"都喜欢充足阳光", "向日葵可以作为背景"},
				Conflicts: []string{"向日葵高大可能遮挡玫瑰", "根系竞争激烈"},
				Recommendations: []string{"向日葵种在玫瑰北侧或西侧", "保持3米以上距离"},
			},
			"lavender": {
				Score: 7,
				Level: "良好兼容",
				Benefits: []string{"都喜欢排水良好的土壤", "薰衣草驱虫保护向日葵"},
				Conflicts: []string{"向日葵可能遮挡薰衣草的阳光"},
				Recommendations: []string{"薰衣草种在向日葵南侧", "保持2米距离"},
			},
			"lily": {
				Score: 8,
				Level: "良好兼容",
				Benefits: []string{"向日葵为百合提供适度遮阴", "根系深度不同，竞争较少"},
				Conflicts: []string{},
				Recommendations: []string{"百合种在向日葵东侧或南侧", "距离1.5-2米"},
			},
			"tulip": {
				Score: 6,
				Level: "中等兼容",
				Benefits: []string{"时间互补，郁金香春季开花，向日葵夏季开花"},
				Conflicts: []string{"向日葵的深根系可能影响郁金香球茎"},
				Recommendations: []string{"郁金香种在向日葵前方", "保持2米距离"},
			},
		},
		"lavender": {
			"rose": {
				Score: 9,
				Level: "高度兼容",
				Benefits: []string{"天然驱虫保护玫瑰", "香味互补", "都喜欢排水良好土壤"},
				Conflicts: []string{},
				Recommendations: []string{"近距离种植，1米距离即可", "薰衣草围绕玫瑰种植"},
			},
			"sunflower": {
				Score: 7,
				Level: "良好兼容",
				Benefits: []string{"薰衣草的驱虫特性", "土壤排水需求相似"},
				Conflicts: []string{"向日葵可能遮挡薰衣草"},
				Recommendations: []string{"薰衣草种在向日葵南侧", "保持适当距离"},
			},
			"lily": {
				Score: 5,
				Level: "一般兼容",
				Benefits: []string{"薰衣草的驱虫特性"},
				Conflicts: []string{"排水需求差异较大", "土壤酸碱度要求不同"},
				Recommendations: []string{"分开种植", "调节土壤条件"},
			},
			"tulip": {
				Score: 6,
				Level: "中等兼容",
				Benefits: []string{"薰衣草的常绿与郁金香的季节性形成互补"},
				Conflicts: []string{"土壤酸碱度需求不同"},
				Recommendations: []string{"保持1.5米距离", "分区域种植"},
			},
		},
		"lily": {
			"rose": {
				Score: 7,
				Level: "良好兼容",
				Benefits: []string{"互补的光照需求", "开花时间错开"},
				Conflicts: []string{"水分需求有差异"},
				Recommendations: []string{"百合种在玫瑰的半阴处", "1.5米距离"},
			},
			"sunflower": {
				Score: 8,
				Level: "良好兼容",
				Benefits: []string{"向日葵提供理想的半阴环境", "根系深度不同"},
				Conflicts: []string{},
				Recommendations: []string{"百合种在向日葵东侧", "1.5-2米距离"},
			},
			"lavender": {
				Score: 5,
				Level: "一般兼容",
				Benefits: []string{"薰衣草的驱虫保护"},
				Conflicts: []string{"土壤和水分需求差异大"},
				Recommendations: []string{"分开种植区域", "土壤条件分别调节"},
			},
			"tulip": {
				Score: 9,
				Level: "高度兼容",
				Benefits: []string{"都是球茎植物，生长习性相似", "开花时间接近，视觉效果好", "都喜欢春季湿润环境"},
				Conflicts: []string{},
				Recommendations: []string{"可以近距离群植", "1米距离即可"},
			},
		},
		"tulip": {
			"rose": {
				Score: 8,
				Level: "良好兼容",
				Benefits: []string{"时间互补，春夏开花接力", "郁金香低矮不遮挡玫瑰"},
				Conflicts: []string{"夏季休眠期水分需求不同"},
				Recommendations: []string{"郁金香作为玫瑰的前景", "1-1.5米距离"},
			},
			"sunflower": {
				Score: 6,
				Level: "中等兼容",
				Benefits: []string{"时间完全错开，互不影响"},
				Conflicts: []string{"向日葵的深根可能影响球茎"},
				Recommendations: []string{"保持足够距离", "2米以上"},
			},
			"lavender": {
				Score: 6,
				Level: "中等兼容",
				Benefits: []string{"薰衣草的常绿特性补充郁金香的季节性"},
				Conflicts: []string{"土壤酸碱度需求不同"},
				Recommendations: []string{"分区种植", "1.5米距离"},
			},
			"lily": {
				Score: 9,
				Level: "高度兼容",
				Benefits: []string{"同为球茎植物", "生长需求相似", "开花时间相近，效果壮观"},
				Conflicts: []string{},
				Recommendations: []string{"可以混合群植", "0.5-1米距离"},
			},
		},
	}
	
	if compat, ok := compatibilityMatrix[flower1][flower2]; ok {
		return compat
	} else if compat, ok := compatibilityMatrix[flower2][flower1]; ok {
		return compat
	}
	
	// 默认兼容性
	return CompatibilityResult{
		Score: 5,
		Level: "未知兼容性",
		Benefits: []string{"需要进一步观察"},
		Conflicts: []string{"未知潜在冲突"},
		Recommendations: []string{"建议保持安全距离"},
	}
}

// analyzeSpacing 分析两个花卉之间的距离
func analyzeSpacing(flower1, flower2 string, distance float64) SpacingResult {
	recommended := getRecommendedDistance(flower1, flower2)
	recommendedValue := parseDistance(recommended)
	
	var status string
	var impact []string
	var suggestions []string
	
	if distance < recommendedValue*0.7 {
		status = "距离过近"
		impact = []string{"可能存在根系竞争", "光照遮挡风险", "病虫害传播风险增加"}
		suggestions = []string{"考虑调整位置", "增加距离", "加强日常维护"}
	} else if distance > recommendedValue*1.5 {
		status = "距离较远"
		impact = []string{"空间利用率低", "视觉效果可能分散"}
		suggestions = []string{"可以适当拉近距离", "考虑在中间添加其他植物"}
	} else {
		status = "距离适宜"
		impact = []string{"空间利用合理", "相互影响最小", "有利于各自生长"}
		suggestions = []string{"维持当前距离", "定期观察生长状况"}
	}
	
	return SpacingResult{
		RecommendedDistance: recommended,
		Status:              status,
		Impact:              impact,
		Suggestions:         suggestions,
	}
}

// analyzeInteraction 分析两个花卉之间的相互作用
func analyzeInteraction(flower1, flower2 map[string]interface{}) map[string]interface{} {
	name1 := flower1["name"].(string)
	name2 := flower2["name"].(string)
	
	compatibility := calculateCompatibility(name1, name2)
	
	// 计算实际距离（如果有位置信息）
	var actualDistance float64 = 0
	if x1, ok := flower1["x"].(float64); ok {
		if y1, ok := flower1["y"].(float64); ok {
			if x2, ok := flower2["x"].(float64); ok {
				if y2, ok := flower2["y"].(float64); ok {
					actualDistance = math.Sqrt(math.Pow(x2-x1, 2) + math.Pow(y2-y1, 2))
				}
			}
		}
	}
	
	interaction := map[string]interface{}{
		"flower1":             name1,
		"flower2":             name2,
		"compatibility_score": compatibility.Score,
		"compatibility_level": compatibility.Level,
		"benefits":            compatibility.Benefits,
		"conflicts":           compatibility.Conflicts,
	}
	
	if actualDistance > 0 {
		spacing := analyzeSpacing(name1, name2, actualDistance)
		interaction["actual_distance"] = fmt.Sprintf("%.1f米", actualDistance)
		interaction["spacing_status"] = spacing.Status
		interaction["spacing_impact"] = spacing.Impact
	}
	
	return interaction
}

// assessOverallHarmony 评估整体和谐度
func assessOverallHarmony(interactions []map[string]interface{}) HarmonyAssessment {
	totalScore := 0
	totalInteractions := len(interactions)
	issues := make([]string, 0)
	suggestions := make([]string, 0)
	
	for _, interaction := range interactions {
		score := interaction["compatibility_score"].(int)
		totalScore += score
		
		if score < 6 {
			flower1 := interaction["flower1"].(string)
			flower2 := interaction["flower2"].(string)
			issues = append(issues, fmt.Sprintf("%s与%s兼容性较低", flower1, flower2))
		}
		
		if spacingStatus, ok := interaction["spacing_status"].(string); ok {
			if spacingStatus == "距离过近" {
				flower1 := interaction["flower1"].(string)
				flower2 := interaction["flower2"].(string)
				issues = append(issues, fmt.Sprintf("%s与%s距离过近", flower1, flower2))
			}
		}
	}
	
	averageScore := 5
	if totalInteractions > 0 {
		averageScore = totalScore / totalInteractions
	}
	
	var harmonyLevel string
	if averageScore >= 8 {
		harmonyLevel = "高度和谐"
		suggestions = append(suggestions, "整体布局很好，继续保持")
	} else if averageScore >= 6 {
		harmonyLevel = "基本和谐"
		suggestions = append(suggestions, "可以进行小幅调整优化")
	} else {
		harmonyLevel = "需要优化"
		suggestions = append(suggestions, "建议重新规划部分植物位置", "考虑调整种植密度")
	}
	
	if len(issues) == 0 {
		suggestions = append(suggestions, "注意定期维护和观察", "根据季节变化调整管理方式")
	}
	
	return HarmonyAssessment{
		HarmonyLevel: harmonyLevel,
		Score:        averageScore,
		Issues:       issues,
		Suggestions:  suggestions,
	}
}

// getRecommendedDistance 获取推荐距离
func getRecommendedDistance(flower1, flower2 string) string {
	// 基于植物特性的距离推荐
	distanceMatrix := map[string]map[string]string{
		"rose": {
			"sunflower": "2.5-3米",
			"lavender":  "1-1.5米",
			"lily":      "1.5米",
			"tulip":     "1-1.5米",
		},
		"sunflower": {
			"rose":     "2.5-3米",
			"lavender": "2米",
			"lily":     "1.5-2米",
			"tulip":    "2米",
		},
		"lavender": {
			"rose":      "1-1.5米",
			"sunflower": "2米",
			"lily":      "2-3米",
			"tulip":     "1.5米",
		},
		"lily": {
			"rose":      "1.5米",
			"sunflower": "1.5-2米",
			"lavender":  "2-3米",
			"tulip":     "0.5-1米",
		},
		"tulip": {
			"rose":      "1-1.5米",
			"sunflower": "2米",
			"lavender":  "1.5米",
			"lily":      "0.5-1米",
		},
	}
	
	if dist, ok := distanceMatrix[flower1][flower2]; ok {
		return dist
	} else if dist, ok := distanceMatrix[flower2][flower1]; ok {
		return dist
	}
	
	return "1.5-2米"
}

// parseDistance 解析距离字符串为数值
func parseDistance(distanceStr string) float64 {
	// 简化解析，取中间值
	switch distanceStr {
	case "0.5-1米":
		return 0.75
	case "1-1.5米":
		return 1.25
	case "1.5米":
		return 1.5
	case "1.5-2米":
		return 1.75
	case "2米":
		return 2.0
	case "2-3米":
		return 2.5
	case "2.5-3米":
		return 2.75
	default:
		return 1.5
	}
}

// getPlantingTips 获取种植建议
func getPlantingTips(flower string) []string {
	tips := map[string][]string{
		"rose": {
			"选择排水良好的位置",
			"确保充足光照",
			"与薰衣草搭配可天然驱虫",
			"预留足够空间便于修剪",
		},
		"sunflower": {
			"种植在最南侧或最高处",
			"避免遮挡其他植物",
			"需要深厚土壤支撑根系",
			"考虑支撑杆防止倒伏",
		},
		"lavender": {
			"边缘种植效果最佳",
			"确保excellent排水",
			"可作为天然边界和驱虫植物",
			"定期修剪保持形状",
		},
		"lily": {
			"选择半阴位置",
			"群植效果更佳",
			"保护球茎免受过度干燥",
			"考虑与其他球茎植物搭配",
		},
		"tulip": {
			"适合前景种植",
			"注意夏季休眠期管理",
			"可与百合等球茎植物混植",
			"春季需要充足水分",
		},
	}
	
	if plantingTips, ok := tips[flower]; ok {
		return plantingTips
	}
	
	return []string{"遵循植物的基本生长需求", "定期观察和维护"}
}
