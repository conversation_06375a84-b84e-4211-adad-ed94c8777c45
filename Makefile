# GreenSoulAI Makefile

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=gofmt
GOLINT=golangci-lint

# Build parameters
BINARY_NAME=greensoulai
BINARY_PATH=./bin/$(BINARY_NAME)
MAIN_PATH=./cmd/greensoulai

# Version information
VERSION ?= $(shell git describe --tags --always --dirty)
BUILD_TIME ?= $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT ?= $(shell git rev-parse HEAD)

# Linker flags
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

.PHONY: all build build-cli clean test coverage deps fmt lint help

# Default target
all: clean deps fmt lint test build

# Build the binary
build:
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p bin
	$(GOBUILD) $(LDFLAGS) -o $(BINARY_PATH) $(MAIN_PATH)

# Build CLI tool (alias for build)
build-cli: build
	@echo "CLI tool built successfully at $(BINARY_PATH)"
	@echo "You can now use: $(BINARY_PATH) --help"

# Clean build artifacts
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	@rm -rf bin/
	@rm -f coverage.out coverage.html

# Run tests
test:
	@echo "Running tests..."
	$(GOTEST) -v -race -coverprofile=coverage.out ./...

# Run tests with coverage
coverage: test
	@echo "Generating coverage report..."
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Install dependencies
deps:
	@echo "Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# Format code
fmt:
	@echo "Formatting code..."
	$(GOFMT) -s -w .

# Run linter
lint:
	@echo "Running linter..."
	$(GOLINT) run ./...

# Run benchmarks
bench:
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

# Install the binary
install: build
	@echo "Installing $(BINARY_NAME)..."
	@cp $(BINARY_PATH) $(GOPATH)/bin/

# Run the application
run: build
	@echo "Running $(BINARY_NAME)..."
	$(BINARY_PATH)

# Development server with auto-reload
dev:
	@echo "Starting development server..."
	@which air > /dev/null || (echo "Installing air..." && go install github.com/cosmtrek/air@latest)
	@air

# Docker build
docker-build:
	@echo "Building Docker image..."
	docker build -t greensoulai:$(VERSION) .

# Docker run
docker-run:
	@echo "Running Docker container..."
	docker run -it --rm greensoulai:$(VERSION)

# Release build for multiple platforms
release:
	@echo "Building release binaries..."
	@mkdir -p bin/release
	# Linux amd64
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/release/$(BINARY_NAME)-linux-amd64 $(MAIN_PATH)
	# Linux arm64
	GOOS=linux GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o bin/release/$(BINARY_NAME)-linux-arm64 $(MAIN_PATH)
	# macOS amd64
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/release/$(BINARY_NAME)-darwin-amd64 $(MAIN_PATH)
	# macOS arm64
	GOOS=darwin GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o bin/release/$(BINARY_NAME)-darwin-arm64 $(MAIN_PATH)
	# Windows amd64
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/release/$(BINARY_NAME)-windows-amd64.exe $(MAIN_PATH)

# Security scan
security:
	@echo "Running security scan..."
	@which gosec > /dev/null || (echo "Installing gosec..." && go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest)
	gosec ./...

# Vulnerability check
vuln:
	@echo "Checking for vulnerabilities..."
	@which govulncheck > /dev/null || (echo "Installing govulncheck..." && go install golang.org/x/vuln/cmd/govulncheck@latest)
	govulncheck ./...

# Generate documentation
docs:
	@echo "Generating documentation..."
	@which godoc > /dev/null || (echo "Installing godoc..." && go install golang.org/x/tools/cmd/godoc@latest)
	@echo "Documentation server will be available at http://localhost:6060"
	godoc -http=:6060

# Help
help:
	@echo "Available targets:"
	@echo "  build     - Build the binary"
	@echo "  build-cli - Build CLI tool (alias for build)"
	@echo "  clean     - Clean build artifacts"
	@echo "  test      - Run tests"
	@echo "  coverage  - Generate test coverage report"
	@echo "  deps      - Install dependencies"
	@echo "  fmt       - Format code"
	@echo "  lint      - Run linter"
	@echo "  bench     - Run benchmarks"
	@echo "  install   - Install binary to GOPATH/bin"
	@echo "  run       - Build and run the application"
	@echo "  dev       - Start development server with auto-reload"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo "  release   - Build release binaries for multiple platforms"
	@echo "  security  - Run security scan"
	@echo "  vuln      - Check for vulnerabilities"
	@echo "  docs      - Generate and serve documentation"
	@echo "  help      - Show this help message"
