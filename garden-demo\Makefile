.PHONY: build run test clean deps

# Go参数
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 项目参数
BINARY_NAME=garden-demo
MAIN_PATH=./cmd/main.go

# 构建
build:
	$(GOBUILD) -o $(BINARY_NAME) $(MAIN_PATH)

# 运行
run:
	$(GOCMD) run $(MAIN_PATH)

# 测试
test:
	$(GOTEST) -v ./...

# 测试覆盖率
test-coverage:
	$(GOTEST) -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out

# 清理
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f coverage.out

# 依赖管理
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# 更新依赖
update:
	$(GOMOD) download
	$(GOMOD) tidy
	$(GOGET) -u ./...

# 格式化代码
fmt:
	gofmt -s -w .
	$(GOCMD) mod tidy

# 静态检查
lint:
	golangci-lint run

# 开发环境设置
setup:
	cp .env.example .env
	$(MAKE) deps

# 帮助
help:
	@echo "可用命令:"
	@echo "  build        构建项目"
	@echo "  run          运行项目" 
	@echo "  test         运行测试"
	@echo "  test-coverage 运行测试并生成覆盖率报告"
	@echo "  clean        清理构建文件"
	@echo "  deps         下载依赖"
	@echo "  update       更新依赖"
	@echo "  fmt          格式化代码"
	@echo "  lint         静态检查"
	@echo "  setup        设置开发环境"
	@echo "  help         显示帮助信息"
