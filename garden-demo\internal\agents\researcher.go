package agents

import (
	"fmt"
	
	"github.com/ynl/greensoulai/internal/agent"
	"github.com/ynl/greensoulai/internal/llm"
	"github.com/ynl/greensoulai/pkg/events"
	"github.com/ynl/greensoulai/pkg/logger"
	"github.com/username/garden-demo/internal/tools"
)

// NewResearcherAgent 创建researcher智能体
func NewResearcherAgent(llmProvider llm.LLM, eventBus events.EventBus, log logger.Logger) (agent.Agent, error) {
	config := agent.AgentConfig{
		Role:      "高级研究员",
		Goal:      "进行深入的研究和分析", 
		Backstory: "你是一位经验丰富的研究专家，擅长收集、分析和总结信息。",
		LLM:       llmProvider,
		EventBus:  eventBus,
		Logger:    log,
		ExecutionConfig: agent.ExecutionConfig{
			MaxIterations:   25,
			Timeout:        30 * time.Minute,
			VerboseLogging: true,
		},
	}
	
	a, err := agent.NewBaseAgent(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create agent: %w", err)
	}
	// 添加工具
	tools := []agent.Tool{
		tools.NewSearchToolTool(),
		tools.NewAnalysisToolTool(),
	}
	
	for _, tool := range tools {
		if err := a.AddTool(tool); err != nil {
			return nil, fmt.Errorf("failed to add tool: %w", err)
		}
	}
	
	return a, nil
}
