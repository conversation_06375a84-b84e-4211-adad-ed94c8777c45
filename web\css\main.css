/* 全局样式和布局 */
:root {
    /* 主题色彩 */
    --primary-color: #28a745;
    --secondary-color: #6c757d;
    --background-color: #f8f9fa;
    --surface-color: #ffffff;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    
    /* Agent 颜色 */
    --rose-color: #ff6b9d;
    --sunflower-color: #ffd93d;
    --lavender-color: #b19cd9;
    --lily-color: #e8f5e8;
    --tulip-color: #ff4757;
    --system-color: #6c757d;
    --garden-color: #28a745;
    
    /* 尺寸 */
    --header-height: 60px;
    --agents-panel-width: 280px;
    --system-panel-width: 240px;
    --border-radius: 8px;
    --border-radius-small: 4px;
    
    /* 动画 */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
}

/* 主容器布局 */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部标题栏 */
.app-header {
    height: var(--header-height);
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: var(--shadow-light);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.garden-icon {
    font-size: 1.8rem;
    animation: gentle-bounce 2s ease-in-out infinite;
}

@keyframes gentle-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-2px); }
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.connection-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 连接状态 */
.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: var(--background-color);
    border-radius: var(--border-radius-small);
    font-size: 0.9rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--secondary-color);
    transition: var(--transition-fast);
}

.status-indicator.connected {
    background: var(--primary-color);
    animation: pulse 2s ease-in-out infinite;
}

.status-indicator.connecting {
    background: var(--sunflower-color);
    animation: blink 1s ease-in-out infinite;
}

.status-indicator.disconnected {
    background: var(--tulip-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* 连接按钮 */
.connect-btn {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    padding: 6px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    transition: var(--transition-fast);
    color: var(--text-primary);
}

.connect-btn:hover {
    background: var(--background-color);
    border-color: var(--primary-color);
}

.connect-btn.connecting {
    background: var(--sunflower-color);
    color: #000;
    border-color: var(--sunflower-color);
}

.connect-btn.connected {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.connect-btn.disconnected {
    background: var(--tulip-color);
    color: white;
    border-color: var(--tulip-color);
}

.connect-icon {
    font-size: 1rem;
}

/* 主题切换按钮 */
.theme-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius-small);
    transition: var(--transition-fast);
}

.theme-toggle:hover {
    background: var(--background-color);
}

/* 主内容区域 */
.app-main {
    flex: 1;
    display: grid;
    grid-template-columns: var(--agents-panel-width) 1fr var(--system-panel-width);
    height: calc(100vh - var(--header-height));
    overflow: hidden;
}

/* 面板通用样式 */
.agents-panel,
.system-panel {
    background: var(--surface-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.system-panel {
    border-right: none;
    border-left: 1px solid var(--border-color);
}

.panel-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--background-color);
}

.panel-header h2 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* 消息区域 */
.messages-area {
    display: flex;
    flex-direction: column;
    background: var(--background-color);
    overflow: hidden;
}

.messages-header {
    padding: 16px 20px;
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.messages-header h2 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.messages-controls {
    display: flex;
    gap: 8px;
}

.control-btn {
    background: none;
    border: none;
    padding: 6px 8px;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    font-size: 1rem;
    transition: var(--transition-fast);
}

.control-btn:hover {
    background: var(--background-color);
}

.messages-container {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.messages-list {
    height: 100%;
    overflow-y: auto;
    padding: 20px;
    scroll-behavior: smooth;
}

/* 欢迎消息 */
.welcome-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.welcome-content {
    max-width: 400px;
    padding: 40px;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.welcome-content h3 {
    color: var(--primary-color);
    margin-bottom: 12px;
    font-size: 1.3rem;
}

.welcome-content p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

/* 加载指示器 */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .app-main {
        grid-template-columns: 240px 1fr 200px;
    }
    
    :root {
        --agents-panel-width: 240px;
        --system-panel-width: 200px;
    }
}

@media (max-width: 768px) {
    .app-main {
        grid-template-columns: 1fr;
    }
    
    .agents-panel,
    .system-panel {
        display: none;
    }
    
    .app-title {
        font-size: 1.2rem;
    }
    
    .header-right {
        gap: 8px;
    }
}

/* 滚动条样式 */
.messages-list::-webkit-scrollbar {
    width: 6px;
}

.messages-list::-webkit-scrollbar-track {
    background: var(--background-color);
}

.messages-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
