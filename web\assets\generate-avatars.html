<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Avatar Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .avatar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .avatar-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .avatar-svg {
            margin-bottom: 10px;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>GreenSoulAI Agent Avatar Generator</h1>
    <p>点击下载按钮保存每个 Agent 的头像 SVG 文件</p>
    
    <div class="avatar-grid" id="avatarGrid"></div>

    <script>
        const agents = [
            { name: 'Rose', role: '玫瑰', color: '#ff6b9d', icon: '🌹' },
            { name: 'Sunflower', role: '向日葵', color: '#ffd93d', icon: '🌻' },
            { name: 'Lavender', role: '薰衣草', color: '#b19cd9', icon: '💜' },
            { name: 'Lily', role: '百合', color: '#e8f5e8', icon: '🌸', textColor: '#2d5a2d' },
            { name: 'Tulip', role: '郁金香', color: '#ff4757', icon: '🌷' },
            { name: 'System', role: '系统', color: '#6c757d', icon: '⚙️' },
            { name: 'Garden', role: '花园', color: '#28a745', icon: '🌿' }
        ];

        function generateSVG(agent) {
            const textColor = agent.textColor || '#ffffff';
            const initial = agent.name[0];
            
            return `<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                <circle cx="32" cy="32" r="32" fill="${agent.color}"/>
                <text x="32" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="${textColor}">${initial}</text>
                <text x="32" y="20" text-anchor="middle" font-size="16">${agent.icon}</text>
            </svg>`;
        }

        function downloadSVG(svgContent, filename) {
            const blob = new Blob([svgContent], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function createAvatarCard(agent) {
            const svgContent = generateSVG(agent);
            
            const card = document.createElement('div');
            card.className = 'avatar-card';
            
            card.innerHTML = `
                <div class="avatar-svg">${svgContent}</div>
                <h3>${agent.name}</h3>
                <p>${agent.role}</p>
                <button class="download-btn" onclick="downloadSVG(\`${svgContent}\`, '${agent.name.toLowerCase()}.svg')">
                    下载 SVG
                </button>
            `;
            
            return card;
        }

        // 生成所有头像卡片
        const grid = document.getElementById('avatarGrid');
        agents.forEach(agent => {
            grid.appendChild(createAvatarCard(agent));
        });

        // 全局函数供按钮调用
        window.downloadSVG = downloadSVG;
    </script>
</body>
</html>
