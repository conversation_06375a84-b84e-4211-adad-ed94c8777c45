/* 组件样式 */

/* Agent 卡片样式 */
.agents-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.agent-card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    margin-bottom: 12px;
    transition: var(--transition-medium);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.agent-card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-1px);
}

.agent-card.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

.agent-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--agent-color, var(--secondary-color));
    transition: var(--transition-fast);
}

.agent-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.agent-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--agent-color, var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.agent-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.agent-info {
    flex: 1;
}

.agent-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 2px;
}

.agent-role {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.agent-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.agent-status-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--secondary-color);
    transition: var(--transition-fast);
}

.agent-status-indicator.idle {
    background: var(--secondary-color);
}

.agent-status-indicator.thinking {
    background: var(--sunflower-color);
    animation: pulse 1.5s ease-in-out infinite;
}

.agent-status-indicator.responding {
    background: var(--primary-color);
    animation: pulse 1s ease-in-out infinite;
}

.agent-last-activity {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 8px;
    text-align: right;
}

/* 消息样式 */
.message {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    padding: 12px;
    border-radius: var(--border-radius);
    background: var(--surface-color);
    border: 1px solid transparent;
    transition: var(--transition-fast);
    animation: message-enter 0.3s ease-out;
}

@keyframes message-enter {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message:hover {
    border-color: var(--border-color);
    box-shadow: var(--shadow-light);
}

.message.highlighted {
    border-color: var(--primary-color);
    background: rgba(40, 167, 69, 0.05);
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--agent-color, var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    font-weight: 600;
    flex-shrink: 0;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.message-author {
    font-weight: 600;
    color: var(--agent-color, var(--text-primary));
    font-size: 0.9rem;
}

.message-role {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.message-timestamp {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-left: auto;
}

.message-body {
    color: var(--text-primary);
    line-height: 1.5;
    word-wrap: break-word;
}

.message-task {
    background: var(--background-color);
    border-radius: var(--border-radius-small);
    padding: 8px 12px;
    margin-top: 8px;
    font-size: 0.85rem;
    color: var(--text-secondary);
    border-left: 3px solid var(--agent-color, var(--secondary-color));
}

/* 消息类型样式 */
.message.agent-message .message-avatar {
    background: var(--agent-color);
}

.message.system-message .message-avatar {
    background: var(--system-color);
}

.message.garden-status .message-avatar {
    background: var(--garden-color);
}

.message.task-message .message-avatar {
    background: var(--primary-color);
}

/* 消息状态样式 */
.message-type-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: var(--border-radius-small);
    background: var(--background-color);
    color: var(--text-secondary);
    margin-left: 8px;
}

.message-type-indicator.start {
    background: rgba(255, 217, 61, 0.2);
    color: #b8860b;
}

.message-type-indicator.response {
    background: rgba(40, 167, 69, 0.2);
    color: var(--primary-color);
}

.message-type-indicator.task-start {
    background: rgba(0, 123, 255, 0.2);
    color: #0056b3;
}

.message-type-indicator.task-complete {
    background: rgba(40, 167, 69, 0.2);
    color: var(--primary-color);
}

/* 系统信息面板样式 */
.system-info {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.info-card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    margin-bottom: 16px;
}

.info-card h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid var(--background-color);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.stat-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.system-logs {
    max-height: 200px;
    overflow-y: auto;
    font-size: 0.8rem;
}

.log-entry {
    padding: 4px 0;
    color: var(--text-secondary);
    border-bottom: 1px solid var(--background-color);
}

.log-entry:last-child {
    border-bottom: none;
}

.log-timestamp {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

/* 面板切换按钮 */
.panel-toggle {
    background: none;
    border: none;
    font-size: 1rem;
    cursor: pointer;
    padding: 4px;
    border-radius: var(--border-radius-small);
    transition: var(--transition-fast);
}

.panel-toggle:hover {
    background: var(--background-color);
}

.agents-count {
    font-size: 0.8rem;
    color: var(--text-secondary);
    background: var(--background-color);
    padding: 4px 8px;
    border-radius: var(--border-radius-small);
}

/* 空状态样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-secondary);
    text-align: center;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state-text {
    font-size: 1rem;
    margin-bottom: 8px;
}

.empty-state-subtext {
    font-size: 0.85rem;
    opacity: 0.7;
}
