# GreenSoulAI 花园聊天室前端

这是 GreenSoulAI Garden 示例的实时聊天界面前端，可以实时观看多个智能体之间的对话。

## 🌟 功能特性

- **实时聊天界面**: 通过 WebSocket 实时显示智能体对话
- **多智能体支持**: 支持 Rose、Sunflower、Lavender、Lily、Tulip 五个智能体
- **美观的 UI**: 现代化的聊天界面设计，支持暗色/亮色主题
- **状态监控**: 实时显示连接状态、在线用户数、系统运行时间等
- **响应式设计**: 支持桌面和移动设备

## 🚀 快速开始

### 1. 启动后端服务

首先需要启动 GreenSoulAI Garden 后端服务：

```bash
# 在项目根目录下
cd examples/garden

# 设置 API 密钥
export OPENROUTER_API_KEY="your-openrouter-api-key-here"

# 启动服务（包含 WebSocket 服务器）
go run ./cmd
```

### 2. 访问聊天界面

后端服务启动后，会自动启动 WebSocket 服务器在 `:8080` 端口。

打开浏览器访问：
```
http://localhost:8080
```

## 📁 文件结构

```
web/
├── index.html              # 主页面
├── css/
│   ├── main.css           # 主样式文件
│   ├── components.css     # 组件样式
│   └── themes.css         # 主题样式
├── js/
│   ├── main.js           # 主应用程序
│   ├── websocket.js      # WebSocket 连接管理
│   ├── message-handler.js # 消息处理
│   └── ui-manager.js     # UI 状态管理
├── assets/
│   ├── avatars/          # 智能体头像
│   └── icons/            # 图标文件
└── README.md             # 本文件
```

## 🎨 界面说明

### 主要区域

1. **顶部标题栏**: 显示应用标题、连接状态和主题切换按钮
2. **左侧面板**: 显示所有智能体的状态和最后活动时间
3. **中间消息区**: 实时显示智能体之间的对话
4. **右侧面板**: 显示系统统计信息和日志

### 消息类型

- **🤔 思考中**: 智能体开始处理任务
- **💬 回复**: 智能体的回复内容
- **📋 任务开始**: 新任务开始
- **✅任务完成**: 任务完成
- **ℹ️ 信息**: 系统信息
- **📊 状态**: 花园状态更新

## ⌨️ 快捷键

- `Ctrl/Cmd + K`: 清空消息
- `Ctrl/Cmd + D`: 切换主题
- `End`: 滚动到底部
- `Escape`: 清除高亮

## 🔧 开发者工具

在浏览器控制台中，可以使用以下调试工具：

```javascript
// 获取应用状态
window.GardenChatDebug.getStatus()

// 手动重连 WebSocket
window.GardenChatDebug.reconnect()

// 导出聊天记录
window.GardenChatDebug.exportChat()

// 清空消息
window.GardenChatDebug.clearMessages()

// 切换主题
window.GardenChatDebug.toggleTheme()
```

## 🌐 API 端点

- `GET /`: 静态文件服务
- `GET /ws`: WebSocket 连接端点
- `GET /health`: 健康检查
- `GET /api/stats`: 系统统计信息

## 🎯 技术栈

- **前端**: HTML5, CSS3, Vanilla JavaScript
- **通信**: WebSocket
- **样式**: CSS Grid, Flexbox, CSS Variables
- **图标**: SVG, Emoji

## 🔄 WebSocket 消息格式

### 接收的消息格式

```json
{
  "type": "agent_message",
  "timestamp": "2024-01-01T12:00:00Z",
  "agent": {
    "name": "Rose",
    "role": "玫瑰",
    "avatar": "/assets/avatars/rose.svg",
    "color": "#ff6b9d"
  },
  "message": {
    "content": "消息内容",
    "messageType": "response",
    "taskDescription": "任务描述"
  }
}
```

### 消息类型

- `agent_message`: 智能体消息
- `task_message`: 任务相关消息
- `system_message`: 系统消息
- `garden_status`: 花园状态消息

## 🐛 故障排除

### 连接问题

1. 确保后端服务正在运行
2. 检查防火墙设置
3. 确认端口 8080 未被占用

### 显示问题

1. 清除浏览器缓存
2. 检查浏览器控制台错误
3. 尝试刷新页面

### 性能问题

1. 使用 `Ctrl+K` 清空历史消息
2. 关闭其他标签页释放内存
3. 检查网络连接质量

## 📝 许可证

本项目遵循 MIT 许可证。
