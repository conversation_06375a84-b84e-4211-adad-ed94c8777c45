package tasks

import (
	"github.com/ynl/greensoulai/internal/agent"
	"github.com/ynl/greensoulai/pkg/events"
	"github.com/ynl/greensoulai/pkg/logger"
)

// NewResearchTaskTask 创建research_task任务
func NewResearchTaskTask(eventBus events.EventBus, log logger.Logger) (agent.Task, error) {
	task := &agent.BaseTask{
		Name:           "research_task",
		Description:    "进行主题研究",
		ExpectedOutput: "详细的研究报告",
		OutputFile:     "research_report.md",
		EventBus:       eventBus,
		Logger:         log,
	}
	
	return task, nil
}
